import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

// 导入路由
import dataSourceRoutes from './routes/dataSourceRoutes';
import chatRoutes from './routes/chatRoutes';
import semanticRoutes from './routes/semanticRoutes';

// 导入Redis客户端包装器
import { redisClient } from './utils/redisClient';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 初始化数据库连接
export const prisma = new PrismaClient();

// 导出Redis客户端实例供其他模块使用
export { redisClient };

// 中间件
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API 路由
app.use('/api/data-sources', dataSourceRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/semantic', semanticRoutes);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', err);
  
  res.status(err.status || 500).json({
    error: err.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// 启动服务器
async function startServer() {
  try {
    // 尝试连接Redis（如果配置了的话）
    await redisClient.connect();
    const redisStatus = redisClient.getStatus();

    if (redisStatus.available) {
      console.log('✅ Redis连接成功');
    } else if (redisStatus.enabled) {
      console.log('⚠️ Redis连接失败，将使用无缓存模式');
    } else {
      console.log('📝 Redis未配置，将使用无缓存模式');
    }

    // 测试数据库连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
      console.log(`📊 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🗄️ Redis状态: ${redisStatus.available ? '已连接' : '未连接'}`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭...');

  try {
    await redisClient.disconnect();
    await prisma.$disconnect();
    console.log('✅ 数据库和Redis连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出错:', error);
    process.exit(1);
  }
});

startServer();
